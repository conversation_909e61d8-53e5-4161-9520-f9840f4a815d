{% extends 'admin/base.html.twig' %}

{% block title %}Edit Course - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Course{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_courses') }}">Courses</a></li>
<li class="breadcrumb-item active">Edit Course</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-graduation-cap mr-3" style="font-size: 2rem;"></i>
                        Edit Course
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Back to Courses Button -->
                        <a href="{{ path('admin_courses') }}"
                           class="btn mb-2 mb-md-0"
                           style="width: 45px; height: 45px; border-radius: 50%; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; text-decoration: none;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';"
                           title="Back to Courses">
                            <i class="fas fa-arrow-left" style="font-size: 1.1rem;"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method="post" id="course-form" class="needs-validation" enctype="multipart/form-data" novalidate>
            <input type="hidden" name="_token" value="{{ csrf_token('course_edit') }}">
            <input type="hidden" name="is_active" value="{{ course.isActive ? '1' : '0' }}">
            <div class="card-body">
                    <!-- Single Column Layout -->
                    <div class="row">
                        <div class="col-12">
                            <!-- Course Code and Title Row -->
                            <div class="row">
                                <!-- Course Code -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="code" class="form-label">
                                            <i class="fas fa-hashtag" style="color: #007bff; margin-right: 0.5rem;"></i>
                                            Course Code <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control enhanced-field"
                                               id="code"
                                               name="code"
                                               value="{{ course.code }}"
                                               placeholder="e.g., TRAD101, FIN200"
                                               required
                                               maxlength="10"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;">
                                        <div class="invalid-feedback">
                                            Please provide a valid course code (max 10 characters).
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Title -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="title" class="form-label">
                                            <i class="fas fa-graduation-cap" style="color: #007bff; margin-right: 0.5rem;"></i>
                                            Course Title <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control enhanced-field"
                                               id="title"
                                               name="title"
                                               value="{{ course.title }}"
                                               placeholder="Enter comprehensive course title"
                                               required
                                               maxlength="255"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;">
                                        <div class="invalid-feedback">
                                            Please provide a course title.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Course Details Row -->
                            <div class="row">
                                <!-- Course Category -->
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="category" class="form-label">
                                            <i class="fas fa-tags" style="color: #007bff; margin-right: 0.5rem;" aria-hidden="true"></i>
                                            Category <span class="text-danger" aria-label="required">*</span>
                                        </label>
                                        <select class="form-select enhanced-dropdown"
                                                id="category"
                                                name="category"
                                                required
                                                aria-describedby="category_help category_error"
                                                aria-label="Select a course category"
                                                style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;">
                                            <option value="">Choose a category...</option>
                                            {% for category in categories %}
                                                <option value="{{ category.name }}" {% if course.category == category.name %}selected{% endif %}>{{ category.name }}</option>
                                            {% endfor %}
                                        </select>
                                        <div id="category_error" class="invalid-feedback" role="alert" aria-live="polite">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Level -->
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="level" class="form-label">
                                            <i class="fas fa-layer-group" style="color: #007bff; margin-right: 0.5rem;"></i>
                                            Level <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select enhanced-dropdown"
                                                id="level"
                                                name="level"
                                                required
                                                style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;">
                                            <option value="">Choose a level...</option>
                                            <option value="Beginner" {% if course.level == 'Beginner' %}selected{% endif %}>Beginner</option>
                                            <option value="Intermediate" {% if course.level == 'Intermediate' %}selected{% endif %}>Intermediate</option>
                                            <option value="Advanced" {% if course.level == 'Advanced' %}selected{% endif %}>Advanced</option>
                                            <option value="Expert" {% if course.level == 'Expert' %}selected{% endif %}>Expert</option>
                                        </select>
                                        <div class="invalid-feedback">
                                            Please select a course level.
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Duration -->
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="duration" class="form-label">
                                            <i class="fas fa-clock" style="color: #007bff; margin-right: 0.5rem;"></i>
                                            Duration (minutes) <span class="text-danger">*</span>
                                        </label>
                                        <input type="number"
                                               class="form-control enhanced-field"
                                               id="duration"
                                               name="duration"
                                               value="{{ course.duration }}"
                                               placeholder="e.g., 120"
                                               required
                                               min="1"
                                               max="10080"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;">
                                        <div class="invalid-feedback">
                                            Please provide a valid duration in minutes.
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Price -->
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="price" class="form-label">
                                            <i class="fas fa-dollar-sign" style="color: #007bff; margin-right: 0.5rem;"></i>
                                            Price (USD) <span class="text-danger">*</span>
                                        </label>
                                        <input type="number"
                                               class="form-control enhanced-field"
                                               id="price"
                                               name="price"
                                               value="{{ course.price }}"
                                               placeholder="e.g., 99.99"
                                               required
                                               min="0.01"
                                               step="0.01"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;">
                                        <div class="invalid-feedback">
                                            Please provide a valid price.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Course Description -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="description" class="form-label">
                                            <i class="fas fa-align-left" style="color: #007bff; margin-right: 0.5rem;"></i>
                                            Course Description <span class="text-danger">*</span>
                                        </label>
                                        <textarea class="form-control enhanced-field"
                                                  id="description"
                                                  name="description"
                                                  rows="4"
                                                  placeholder="Provide a comprehensive description of the course content, objectives, and what students will learn..."
                                                  required
                                                  maxlength="2000"
                                                  style="border: 2px solid #ced4da; border-radius: 0.375rem; resize: vertical;">{{ course.description }}</textarea>
                                        <div class="invalid-feedback">
                                            Please provide a course description.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Learning Outcomes Section -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-graduation-cap" style="color: #007bff; margin-right: 0.5rem;"></i>
                                            Learning Outcomes <span class="text-danger">*</span>
                                        </label>
                                        <div id="learning-outcomes-container">
                                            {% if course.learningOutcomes %}
                                                {% for outcome in course.learningOutcomes %}
                                                    <div class="input-group mb-2">
                                                        <input type="text" class="form-control enhanced-field" name="learning_outcomes[]" value="{{ outcome }}" placeholder="e.g., Master advanced chart analysis techniques" required style="border-radius: 0.375rem 0 0 0.375rem;">
                                                        <div class="input-group-append">
                                                            <button type="button" class="btn btn-danger remove-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                                <i class="fas fa-minus"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            {% else %}
                                                <div class="input-group mb-2">
                                                    <input type="text" class="form-control enhanced-field" name="learning_outcomes[]" placeholder="e.g., Master advanced chart analysis techniques" required style="border-radius: 0.375rem 0 0 0.375rem;">
                                                    <div class="input-group-append">
                                                        <button type="button" class="btn btn-success add-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <button type="button" id="add-outcome-btn" class="btn btn-outline-primary btn-sm mt-2">
                                            <i class="fas fa-plus mr-1"></i>Add Learning Outcome
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Features Section -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-star" style="color: #007bff; margin-right: 0.5rem;"></i>
                                            Course Features <span class="text-danger">*</span>
                                        </label>
                                        <div id="features-container">
                                            {% if course.features %}
                                                {% for feature in course.features %}
                                                    <div class="input-group mb-2">
                                                        <input type="text" class="form-control enhanced-field" name="features[]" value="{{ feature }}" placeholder="e.g., Interactive exercises and quizzes" required style="border-radius: 0.375rem 0 0 0.375rem;">
                                                        <div class="input-group-append">
                                                            <button type="button" class="btn btn-danger remove-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                                <i class="fas fa-minus"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            {% else %}
                                                <div class="input-group mb-2">
                                                    <input type="text" class="form-control enhanced-field" name="features[]" placeholder="e.g., Interactive exercises and quizzes" required style="border-radius: 0.375rem 0 0 0.375rem;">
                                                    <div class="input-group-append">
                                                        <button type="button" class="btn btn-success add-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <button type="button" id="add-feature-btn" class="btn btn-outline-primary btn-sm mt-2">
                                            <i class="fas fa-plus mr-1"></i>Add Feature
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Image Upload Section -->
                            <div class="row">
                                <!-- Thumbnail Image -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="thumbnail_image" class="form-label">
                                            <i class="fas fa-image" style="color: #007bff; margin-right: 0.5rem;"></i>
                                            Thumbnail Image (300x200px)
                                        </label>
                                        <input type="file"
                                               class="form-control enhanced-field"
                                               id="thumbnail_image"
                                               name="thumbnail_image"
                                               accept="image/*"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;">
                                        {% if course.thumbnailImage %}
                                            <div class="mt-2">
                                                <img src="{{ course.thumbnailUrl }}" alt="Current thumbnail" class="img-thumbnail" style="max-width: 150px;">
                                                <small class="text-muted d-block">Current thumbnail</small>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Banner Image -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="banner_image" class="form-label">
                                            <i class="fas fa-panorama" style="color: #007bff; margin-right: 0.5rem;"></i>
                                            Banner Image (1200x400px)
                                        </label>
                                        <input type="file"
                                               class="form-control enhanced-field"
                                               id="banner_image"
                                               name="banner_image"
                                               accept="image/*"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;">
                                        {% if course.bannerImage %}
                                            <div class="mt-2">
                                                <img src="{{ course.bannerUrl }}" alt="Current banner" class="img-thumbnail" style="max-width: 200px;">
                                                <small class="text-muted d-block">Current banner</small>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Modules Section -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="has_modules" name="has_modules" value="1" {% if course.hasModules %}checked{% endif %}>
                                            <label class="form-check-label" for="has_modules">
                                                <i class="fas fa-puzzle-piece" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                This course has modules
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Modules Container -->
                            <div id="modules-container" style="display: {% if course.hasModules %}block{% else %}none{% endif %};">
                                <div class="row">
                                    <div class="col-12">
                                        <h5 class="mb-3">
                                            <i class="fas fa-puzzle-piece text-primary mr-2"></i>
                                            Course Modules
                                        </h5>
                                        <div id="modules-list">
                                            {% if course.modules %}
                                                {% for module in course.modules %}
                                                    <div class="module-item card mb-3" data-module-index="{{ loop.index0 }}">
                                                        <div class="card-header bg-light">
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <h6 class="mb-0">Module {{ loop.index }}</h6>
                                                                <button type="button" class="btn btn-sm btn-danger remove-module">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        <div class="card-body">
                                                            <input type="hidden" name="modules[{{ loop.index0 }}][id]" value="{{ module.id }}">
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <div class="form-group">
                                                                        <label class="form-label">Module Code <span class="text-danger">*</span></label>
                                                                        <input type="text" class="form-control" name="modules[{{ loop.index0 }}][code]" value="{{ module.code }}" required>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <div class="form-group">
                                                                        <label class="form-label">Module Title <span class="text-danger">*</span></label>
                                                                        <input type="text" class="form-control" name="modules[{{ loop.index0 }}][title]" value="{{ module.title }}" required>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                        <button type="button" id="add-module-btn" class="btn btn-outline-primary">
                                            <i class="fas fa-plus mr-1"></i>Add Module
                                        </button>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <!-- Action Buttons Section -->
                        <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                            <button type="submit" class="btn btn-lg btn-success" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; transition: all 0.3s ease; min-width: 200px;">
                                <i class="fas fa-save mr-2"></i>
                                Update Course
                            </button>
                            <a href="{{ path('admin_courses') }}" class="btn btn-lg btn-secondary" style="font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; min-width: 200px; background: #6c757d; border-color: #6c757d;">
                                <i class="fas fa-times mr-2"></i>
                                Cancel
                            </a>
                        </div>

                    </div>
                </div>

            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Enhanced form validation
    const form = document.getElementById('course-form');

    // Bootstrap validation
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Enhanced dropdowns with Select2
    $('.enhanced-dropdown').select2({
        placeholder: function() {
            return $(this).data('placeholder') || 'Choose an option...';
        },
        allowClear: true,
        width: '100%',
        theme: 'bootstrap4'
    });

    // Learning Outcomes Management
    let outcomeIndex = {{ course.learningOutcomes ? course.learningOutcomes|length : 1 }};

    $('#add-outcome-btn').click(function() {
        const container = $('#learning-outcomes-container');
        const newOutcome = `
            <div class="input-group mb-2">
                <input type="text" class="form-control enhanced-field" name="learning_outcomes[]" placeholder="e.g., Master advanced chart analysis techniques" required style="border-radius: 0.375rem 0 0 0.375rem;">
                <div class="input-group-append">
                    <button type="button" class="btn btn-danger remove-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newOutcome);
        outcomeIndex++;
    });

    $(document).on('click', '.remove-outcome', function() {
        if ($('#learning-outcomes-container .input-group').length > 1) {
            $(this).closest('.input-group').remove();
        }
    });

    // Features Management
    let featureIndex = {{ course.features ? course.features|length : 1 }};

    $('#add-feature-btn').click(function() {
        const container = $('#features-container');
        const newFeature = `
            <div class="input-group mb-2">
                <input type="text" class="form-control enhanced-field" name="features[]" placeholder="e.g., Interactive exercises and quizzes" required style="border-radius: 0.375rem 0 0 0.375rem;">
                <div class="input-group-append">
                    <button type="button" class="btn btn-danger remove-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newFeature);
        featureIndex++;
    });

    $(document).on('click', '.remove-feature', function() {
        if ($('#features-container .input-group').length > 1) {
            $(this).closest('.input-group').remove();
        }
    });

    // Modules Management
    let moduleIndex = {{ course.modules ? course.modules|length : 0 }};

    $('#has_modules').change(function() {
        if ($(this).is(':checked')) {
            $('#modules-container').show();
        } else {
            $('#modules-container').hide();
        }
    });

    $('#add-module-btn').click(function() {
        const modulesList = $('#modules-list');
        const newModule = `
            <div class="module-item card mb-3" data-module-index="${moduleIndex}">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">Module ${moduleIndex + 1}</h6>
                        <button type="button" class="btn btn-sm btn-danger remove-module">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Module Code <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="modules[${moduleIndex}][code]" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Module Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="modules[${moduleIndex}][title]" required>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        modulesList.append(newModule);
        moduleIndex++;
    });

    $(document).on('click', '.remove-module', function() {
        $(this).closest('.module-item').remove();
        // Renumber modules
        $('#modules-list .module-item').each(function(index) {
            $(this).find('h6').text('Module ' + (index + 1));
        });
    });

    // Enhanced field styling
    $('.enhanced-field, .enhanced-dropdown').on('focus', function() {
        $(this).css('border-color', '#007bff');
        $(this).css('box-shadow', '0 0 0 0.2rem rgba(0, 123, 255, 0.25)');
    }).on('blur', function() {
        $(this).css('border-color', '#ced4da');
        $(this).css('box-shadow', 'none');
    });

    // Initialize tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();
});
</script>
{% endblock %}
