<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/courses/edit.html.twig */
class __TwigTemplate_fb9f337deacad498844e5f8dfb04fa11 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/courses/edit.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/courses/edit.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Edit Course - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Edit Course";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_courses");
        yield "\">Courses</a></li>
<li class=\"breadcrumb-item active\">Edit Course</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Course
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Courses Button -->
                        <a href=\"";
        // line 43
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_courses");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"width: 45px; height: 45px; border-radius: 50%; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; text-decoration: none;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\"
                           title=\"Back to Courses\">
                            <i class=\"fas fa-arrow-left\" style=\"font-size: 1.1rem;\"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" id=\"course-form\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"";
        // line 57
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("course_edit"), "html", null, true);
        yield "\">
            <input type=\"hidden\" name=\"is_active\" value=\"";
        // line 58
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 58, $this->source); })()), "isActive", [], "any", false, false, false, 58)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("1") : ("0"));
        yield "\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Course Code and Title Row -->
                            <div class=\"row\">
                                <!-- Course Code -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"code\" class=\"form-label\">
                                            <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Course Code <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"code\"
                                               name=\"code\"
                                               value=\"";
        // line 76
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 76, $this->source); })()), "code", [], "any", false, false, false, 76), "html", null, true);
        yield "\"
                                               placeholder=\"e.g., TRAD101, FIN200\"
                                               required
                                               maxlength=\"10\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid course code (max 10 characters).
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Title -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Course Title <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               value=\"";
        // line 98
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 98, $this->source); })()), "title", [], "any", false, false, false, 98), "html", null, true);
        yield "\"
                                               placeholder=\"Enter comprehensive course title\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a course title.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Course Details Row -->
                            <div class=\"row\">
                                <!-- Course Category -->
                                <div class=\"col-md-3\">
                                    <div class=\"form-group\">
                                        <label for=\"category\" class=\"form-label\">
                                            <i class=\"fas fa-tags\" style=\"color: #007bff; margin-right: 0.5rem;\" aria-hidden=\"true\"></i>
                                            Category <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-dropdown\"
                                                id=\"category\"
                                                name=\"category\"
                                                required
                                                aria-describedby=\"category_help category_error\"
                                                aria-label=\"Select a course category\"
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">
                                            <option value=\"\">Choose a category...</option>
                                            ";
        // line 127
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["categories"]) || array_key_exists("categories", $context) ? $context["categories"] : (function () { throw new RuntimeError('Variable "categories" does not exist.', 127, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["category"]) {
            // line 128
            yield "                                                <option value=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 128), "html", null, true);
            yield "\" ";
            if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 128, $this->source); })()), "category", [], "any", false, false, false, 128) == CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 128))) {
                yield "selected";
            }
            yield ">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 128), "html", null, true);
            yield "</option>
                                            ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['category'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 130
        yield "                                        </select>
                                        <div id=\"category_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Level -->
                                <div class=\"col-md-3\">
                                    <div class=\"form-group\">
                                        <label for=\"level\" class=\"form-label\">
                                            <i class=\"fas fa-layer-group\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Level <span class=\"text-danger\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-dropdown\"
                                                id=\"level\"
                                                name=\"level\"
                                                required
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">
                                            <option value=\"\">Choose a level...</option>
                                            <option value=\"Beginner\" ";
        // line 150
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 150, $this->source); })()), "level", [], "any", false, false, false, 150) == "Beginner")) {
            yield "selected";
        }
        yield ">Beginner</option>
                                            <option value=\"Intermediate\" ";
        // line 151
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 151, $this->source); })()), "level", [], "any", false, false, false, 151) == "Intermediate")) {
            yield "selected";
        }
        yield ">Intermediate</option>
                                            <option value=\"Advanced\" ";
        // line 152
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 152, $this->source); })()), "level", [], "any", false, false, false, 152) == "Advanced")) {
            yield "selected";
        }
        yield ">Advanced</option>
                                            <option value=\"Expert\" ";
        // line 153
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 153, $this->source); })()), "level", [], "any", false, false, false, 153) == "Expert")) {
            yield "selected";
        }
        yield ">Expert</option>
                                        </select>
                                        <div class=\"invalid-feedback\">
                                            Please select a course level.
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Duration -->
                                <div class=\"col-md-3\">
                                    <div class=\"form-group\">
                                        <label for=\"duration\" class=\"form-label\">
                                            <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Duration (minutes) <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"duration\"
                                               name=\"duration\"
                                               value=\"";
        // line 172
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 172, $this->source); })()), "duration", [], "any", false, false, false, 172), "html", null, true);
        yield "\"
                                               placeholder=\"e.g., 120\"
                                               required
                                               min=\"1\"
                                               max=\"10080\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid duration in minutes.
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Price -->
                                <div class=\"col-md-3\">
                                    <div class=\"form-group\">
                                        <label for=\"price\" class=\"form-label\">
                                            <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Price (USD) <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"price\"
                                               name=\"price\"
                                               value=\"";
        // line 195
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 195, $this->source); })()), "price", [], "any", false, false, false, 195), "html", null, true);
        yield "\"
                                               placeholder=\"e.g., 99.99\"
                                               required
                                               min=\"0.01\"
                                               step=\"0.01\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid price.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Course Description -->
                            <div class=\"row\">
                                <div class=\"col-12\">
                                    <div class=\"form-group\">
                                        <label for=\"description\" class=\"form-label\">
                                            <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Course Description <span class=\"text-danger\">*</span>
                                        </label>
                                        <textarea class=\"form-control enhanced-field\"
                                                  id=\"description\"
                                                  name=\"description\"
                                                  rows=\"4\"
                                                  placeholder=\"Provide a comprehensive description of the course content, objectives, and what students will learn...\"
                                                  required
                                                  maxlength=\"2000\"
                                                  style=\"border: 2px solid #ced4da; border-radius: 0.375rem; resize: vertical;\">";
        // line 223
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 223, $this->source); })()), "description", [], "any", false, false, false, 223), "html", null, true);
        yield "</textarea>
                                        <div class=\"invalid-feedback\">
                                            Please provide a course description.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Learning Outcomes Section -->
                            <div class=\"row\">
                                <div class=\"col-12\">
                                    <div class=\"form-group\">
                                        <label class=\"form-label\">
                                            <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Learning Outcomes <span class=\"text-danger\">*</span>
                                        </label>
                                        <div id=\"learning-outcomes-container\">
                                            ";
        // line 240
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 240, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 240)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 241
            yield "                                                ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 241, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 241));
            foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                // line 242
                yield "                                                    <div class=\"input-group mb-2\">
                                                        <input type=\"text\" class=\"form-control enhanced-field\" name=\"learning_outcomes[]\" value=\"";
                // line 243
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                yield "\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                        <div class=\"input-group-append\">
                                                            <button type=\"button\" class=\"btn btn-danger remove-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                <i class=\"fas fa-minus\"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 251
            yield "                                            ";
        } else {
            // line 252
            yield "                                                <div class=\"input-group mb-2\">
                                                    <input type=\"text\" class=\"form-control enhanced-field\" name=\"learning_outcomes[]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                    <div class=\"input-group-append\">
                                                        <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                            <i class=\"fas fa-plus\"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            ";
        }
        // line 261
        yield "                                        </div>
                                        <button type=\"button\" id=\"add-outcome-btn\" class=\"btn btn-outline-primary btn-sm mt-2\">
                                            <i class=\"fas fa-plus mr-1\"></i>Add Learning Outcome
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Features Section -->
                            <div class=\"row\">
                                <div class=\"col-12\">
                                    <div class=\"form-group\">
                                        <label class=\"form-label\">
                                            <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Course Features <span class=\"text-danger\">*</span>
                                        </label>
                                        <div id=\"features-container\">
                                            ";
        // line 278
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 278, $this->source); })()), "features", [], "any", false, false, false, 278)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 279
            yield "                                                ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 279, $this->source); })()), "features", [], "any", false, false, false, 279));
            foreach ($context['_seq'] as $context["_key"] => $context["feature"]) {
                // line 280
                yield "                                                    <div class=\"input-group mb-2\">
                                                        <input type=\"text\" class=\"form-control enhanced-field\" name=\"features[]\" value=\"";
                // line 281
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["feature"], "html", null, true);
                yield "\" placeholder=\"e.g., Interactive exercises and quizzes\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                        <div class=\"input-group-append\">
                                                            <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                <i class=\"fas fa-minus\"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['feature'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 289
            yield "                                            ";
        } else {
            // line 290
            yield "                                                <div class=\"input-group mb-2\">
                                                    <input type=\"text\" class=\"form-control enhanced-field\" name=\"features[]\" placeholder=\"e.g., Interactive exercises and quizzes\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                    <div class=\"input-group-append\">
                                                        <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                            <i class=\"fas fa-plus\"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            ";
        }
        // line 299
        yield "                                        </div>
                                        <button type=\"button\" id=\"add-feature-btn\" class=\"btn btn-outline-primary btn-sm mt-2\">
                                            <i class=\"fas fa-plus mr-1\"></i>Add Feature
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Image Upload Section -->
                            <div class=\"row\">
                                <!-- Thumbnail Image -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"thumbnail_image\" class=\"form-label\">
                                            <i class=\"fas fa-image\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Thumbnail Image (300x200px)
                                        </label>
                                        <input type=\"file\"
                                               class=\"form-control enhanced-field\"
                                               id=\"thumbnail_image\"
                                               name=\"thumbnail_image\"
                                               accept=\"image/*\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        ";
        // line 322
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 322, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 322)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 323
            yield "                                            <div class=\"mt-2\">
                                                <img src=\"";
            // line 324
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 324, $this->source); })()), "thumbnailUrl", [], "any", false, false, false, 324), "html", null, true);
            yield "\" alt=\"Current thumbnail\" class=\"img-thumbnail\" style=\"max-width: 150px;\">
                                                <small class=\"text-muted d-block\">Current thumbnail</small>
                                            </div>
                                        ";
        }
        // line 328
        yield "                                    </div>
                                </div>

                                <!-- Banner Image -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"banner_image\" class=\"form-label\">
                                            <i class=\"fas fa-panorama\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Banner Image (1200x400px)
                                        </label>
                                        <input type=\"file\"
                                               class=\"form-control enhanced-field\"
                                               id=\"banner_image\"
                                               name=\"banner_image\"
                                               accept=\"image/*\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        ";
        // line 344
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 344, $this->source); })()), "bannerImage", [], "any", false, false, false, 344)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 345
            yield "                                            <div class=\"mt-2\">
                                                <img src=\"";
            // line 346
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 346, $this->source); })()), "bannerUrl", [], "any", false, false, false, 346), "html", null, true);
            yield "\" alt=\"Current banner\" class=\"img-thumbnail\" style=\"max-width: 200px;\">
                                                <small class=\"text-muted d-block\">Current banner</small>
                                            </div>
                                        ";
        }
        // line 350
        yield "                                    </div>
                                </div>
                            </div>

                            <!-- Modules Section -->
                            <div class=\"row\">
                                <div class=\"col-12\">
                                    <div class=\"form-group\">
                                        <div class=\"form-check\">
                                            <input class=\"form-check-input\" type=\"checkbox\" id=\"has_modules\" name=\"has_modules\" value=\"1\" ";
        // line 359
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 359, $this->source); })()), "hasModules", [], "any", false, false, false, 359)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            yield "checked";
        }
        yield ">
                                            <label class=\"form-check-label\" for=\"has_modules\">
                                                <i class=\"fas fa-puzzle-piece\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                This course has modules
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Modules Container -->
                            <div id=\"modules-container\" style=\"display: ";
        // line 370
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 370, $this->source); })()), "hasModules", [], "any", false, false, false, 370)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            yield "block";
        } else {
            yield "none";
        }
        yield ";\">
                                <div class=\"row\">
                                    <div class=\"col-12\">
                                        <h5 class=\"mb-3\">
                                            <i class=\"fas fa-puzzle-piece text-primary mr-2\"></i>
                                            Course Modules
                                        </h5>
                                        <div id=\"modules-list\">
                                            ";
        // line 378
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 378, $this->source); })()), "modules", [], "any", false, false, false, 378)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 379
            yield "                                                ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 379, $this->source); })()), "modules", [], "any", false, false, false, 379));
            $context['loop'] = [
              'parent' => $context['_parent'],
              'index0' => 0,
              'index'  => 1,
              'first'  => true,
            ];
            if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                $length = count($context['_seq']);
                $context['loop']['revindex0'] = $length - 1;
                $context['loop']['revindex'] = $length;
                $context['loop']['length'] = $length;
                $context['loop']['last'] = 1 === $length;
            }
            foreach ($context['_seq'] as $context["_key"] => $context["module"]) {
                // line 380
                yield "                                                    <div class=\"module-item card mb-3\" data-module-index=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 380), "html", null, true);
                yield "\">
                                                        <div class=\"card-header bg-light\">
                                                            <div class=\"d-flex justify-content-between align-items-center\">
                                                                <h6 class=\"mb-0\">Module ";
                // line 383
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index", [], "any", false, false, false, 383), "html", null, true);
                yield "</h6>
                                                                <button type=\"button\" class=\"btn btn-sm btn-danger remove-module\">
                                                                    <i class=\"fas fa-trash\"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        <div class=\"card-body\">
                                                            <input type=\"hidden\" name=\"modules[";
                // line 390
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 390), "html", null, true);
                yield "][id]\" value=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "id", [], "any", false, false, false, 390), "html", null, true);
                yield "\">
                                                            <div class=\"row\">
                                                                <div class=\"col-md-6\">
                                                                    <div class=\"form-group\">
                                                                        <label class=\"form-label\">Module Code <span class=\"text-danger\">*</span></label>
                                                                        <input type=\"text\" class=\"form-control\" name=\"modules[";
                // line 395
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 395), "html", null, true);
                yield "][code]\" value=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "code", [], "any", false, false, false, 395), "html", null, true);
                yield "\" required>
                                                                    </div>
                                                                </div>
                                                                <div class=\"col-md-6\">
                                                                    <div class=\"form-group\">
                                                                        <label class=\"form-label\">Module Title <span class=\"text-danger\">*</span></label>
                                                                        <input type=\"text\" class=\"form-control\" name=\"modules[";
                // line 401
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 401), "html", null, true);
                yield "][title]\" value=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "title", [], "any", false, false, false, 401), "html", null, true);
                yield "\" required>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ";
                ++$context['loop']['index0'];
                ++$context['loop']['index'];
                $context['loop']['first'] = false;
                if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                    --$context['loop']['revindex0'];
                    --$context['loop']['revindex'];
                    $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                }
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['module'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 408
            yield "                                            ";
        }
        // line 409
        yield "                                        </div>
                                        <button type=\"button\" id=\"add-module-btn\" class=\"btn btn-outline-primary\">
                                            <i class=\"fas fa-plus mr-1\"></i>Add Module
                                        </button>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <!-- Action Buttons Section -->
                        <div class=\"d-flex justify-content-between align-items-center flex-wrap gap-3\">
                            <button type=\"submit\" class=\"btn btn-lg btn-success\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; transition: all 0.3s ease; min-width: 200px;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                Update Course
                            </button>
                            <a href=\"";
        // line 425
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_courses");
        yield "\" class=\"btn btn-lg btn-secondary\" style=\"font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; min-width: 200px; background: #6c757d; border-color: #6c757d;\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>

                    </div>
                </div>

            </div>
        </form>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 440
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 441
        yield "<script>
\$(document).ready(function() {
    // Enhanced form validation
    const form = document.getElementById('course-form');

    // Bootstrap validation
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Enhanced dropdowns with Select2
    \$('.enhanced-dropdown').select2({
        placeholder: function() {
            return \$(this).data('placeholder') || 'Choose an option...';
        },
        allowClear: true,
        width: '100%',
        theme: 'bootstrap4'
    });

    // Learning Outcomes Management
    let outcomeIndex = ";
        // line 466
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 466, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 466)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 466, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 466)), "html", null, true)) : (1));
        yield ";

    \$('#add-outcome-btn').click(function() {
        const container = \$('#learning-outcomes-container');
        const newOutcome = `
            <div class=\"input-group mb-2\">
                <input type=\"text\" class=\"form-control enhanced-field\" name=\"learning_outcomes[]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newOutcome);
        outcomeIndex++;
    });

    \$(document).on('click', '.remove-outcome', function() {
        if (\$('#learning-outcomes-container .input-group').length > 1) {
            \$(this).closest('.input-group').remove();
        }
    });

    // Features Management
    let featureIndex = ";
        // line 491
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 491, $this->source); })()), "features", [], "any", false, false, false, 491)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 491, $this->source); })()), "features", [], "any", false, false, false, 491)), "html", null, true)) : (1));
        yield ";

    \$('#add-feature-btn').click(function() {
        const container = \$('#features-container');
        const newFeature = `
            <div class=\"input-group mb-2\">
                <input type=\"text\" class=\"form-control enhanced-field\" name=\"features[]\" placeholder=\"e.g., Interactive exercises and quizzes\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newFeature);
        featureIndex++;
    });

    \$(document).on('click', '.remove-feature', function() {
        if (\$('#features-container .input-group').length > 1) {
            \$(this).closest('.input-group').remove();
        }
    });

    // Modules Management
    let moduleIndex = ";
        // line 516
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 516, $this->source); })()), "modules", [], "any", false, false, false, 516)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 516, $this->source); })()), "modules", [], "any", false, false, false, 516)), "html", null, true)) : (0));
        yield ";

    \$('#has_modules').change(function() {
        if (\$(this).is(':checked')) {
            \$('#modules-container').show();
        } else {
            \$('#modules-container').hide();
        }
    });

    \$('#add-module-btn').click(function() {
        const modulesList = \$('#modules-list');
        const newModule = `
            <div class=\"module-item card mb-3\" data-module-index=\"\${moduleIndex}\">
                <div class=\"card-header bg-light\">
                    <div class=\"d-flex justify-content-between align-items-center\">
                        <h6 class=\"mb-0\">Module \${moduleIndex + 1}</h6>
                        <button type=\"button\" class=\"btn btn-sm btn-danger remove-module\">
                            <i class=\"fas fa-trash\"></i>
                        </button>
                    </div>
                </div>
                <div class=\"card-body\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">Module Code <span class=\"text-danger\">*</span></label>
                                <input type=\"text\" class=\"form-control\" name=\"modules[\${moduleIndex}][code]\" required>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">Module Title <span class=\"text-danger\">*</span></label>
                                <input type=\"text\" class=\"form-control\" name=\"modules[\${moduleIndex}][title]\" required>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        modulesList.append(newModule);
        moduleIndex++;
    });

    \$(document).on('click', '.remove-module', function() {
        \$(this).closest('.module-item').remove();
        // Renumber modules
        \$('#modules-list .module-item').each(function(index) {
            \$(this).find('h6').text('Module ' + (index + 1));
        });
    });

    // Enhanced field styling
    \$('.enhanced-field, .enhanced-dropdown').on('focus', function() {
        \$(this).css('border-color', '#007bff');
        \$(this).css('box-shadow', '0 0 0 0.2rem rgba(0, 123, 255, 0.25)');
    }).on('blur', function() {
        \$(this).css('border-color', '#ced4da');
        \$(this).css('box-shadow', 'none');
    });

    // Initialize tooltips
    \$('[data-bs-toggle=\"tooltip\"]').tooltip();
});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/courses/edit.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  901 => 516,  873 => 491,  845 => 466,  818 => 441,  805 => 440,  780 => 425,  762 => 409,  759 => 408,  736 => 401,  725 => 395,  715 => 390,  705 => 383,  698 => 380,  680 => 379,  678 => 378,  663 => 370,  647 => 359,  636 => 350,  629 => 346,  626 => 345,  624 => 344,  606 => 328,  599 => 324,  596 => 323,  594 => 322,  569 => 299,  558 => 290,  555 => 289,  541 => 281,  538 => 280,  533 => 279,  531 => 278,  512 => 261,  501 => 252,  498 => 251,  484 => 243,  481 => 242,  476 => 241,  474 => 240,  454 => 223,  423 => 195,  397 => 172,  373 => 153,  367 => 152,  361 => 151,  355 => 150,  333 => 130,  318 => 128,  314 => 127,  282 => 98,  257 => 76,  236 => 58,  232 => 57,  215 => 43,  199 => 29,  189 => 25,  186 => 24,  182 => 23,  179 => 22,  169 => 18,  166 => 17,  162 => 16,  158 => 14,  145 => 13,  131 => 9,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Edit Course - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Course{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_courses') }}\">Courses</a></li>
<li class=\"breadcrumb-item active\">Edit Course</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Course
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Courses Button -->
                        <a href=\"{{ path('admin_courses') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"width: 45px; height: 45px; border-radius: 50%; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; text-decoration: none;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\"
                           title=\"Back to Courses\">
                            <i class=\"fas fa-arrow-left\" style=\"font-size: 1.1rem;\"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" id=\"course-form\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('course_edit') }}\">
            <input type=\"hidden\" name=\"is_active\" value=\"{{ course.isActive ? '1' : '0' }}\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Course Code and Title Row -->
                            <div class=\"row\">
                                <!-- Course Code -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"code\" class=\"form-label\">
                                            <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Course Code <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"code\"
                                               name=\"code\"
                                               value=\"{{ course.code }}\"
                                               placeholder=\"e.g., TRAD101, FIN200\"
                                               required
                                               maxlength=\"10\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid course code (max 10 characters).
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Title -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Course Title <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               value=\"{{ course.title }}\"
                                               placeholder=\"Enter comprehensive course title\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a course title.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Course Details Row -->
                            <div class=\"row\">
                                <!-- Course Category -->
                                <div class=\"col-md-3\">
                                    <div class=\"form-group\">
                                        <label for=\"category\" class=\"form-label\">
                                            <i class=\"fas fa-tags\" style=\"color: #007bff; margin-right: 0.5rem;\" aria-hidden=\"true\"></i>
                                            Category <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-dropdown\"
                                                id=\"category\"
                                                name=\"category\"
                                                required
                                                aria-describedby=\"category_help category_error\"
                                                aria-label=\"Select a course category\"
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">
                                            <option value=\"\">Choose a category...</option>
                                            {% for category in categories %}
                                                <option value=\"{{ category.name }}\" {% if course.category == category.name %}selected{% endif %}>{{ category.name }}</option>
                                            {% endfor %}
                                        </select>
                                        <div id=\"category_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Level -->
                                <div class=\"col-md-3\">
                                    <div class=\"form-group\">
                                        <label for=\"level\" class=\"form-label\">
                                            <i class=\"fas fa-layer-group\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Level <span class=\"text-danger\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-dropdown\"
                                                id=\"level\"
                                                name=\"level\"
                                                required
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">
                                            <option value=\"\">Choose a level...</option>
                                            <option value=\"Beginner\" {% if course.level == 'Beginner' %}selected{% endif %}>Beginner</option>
                                            <option value=\"Intermediate\" {% if course.level == 'Intermediate' %}selected{% endif %}>Intermediate</option>
                                            <option value=\"Advanced\" {% if course.level == 'Advanced' %}selected{% endif %}>Advanced</option>
                                            <option value=\"Expert\" {% if course.level == 'Expert' %}selected{% endif %}>Expert</option>
                                        </select>
                                        <div class=\"invalid-feedback\">
                                            Please select a course level.
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Duration -->
                                <div class=\"col-md-3\">
                                    <div class=\"form-group\">
                                        <label for=\"duration\" class=\"form-label\">
                                            <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Duration (minutes) <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"duration\"
                                               name=\"duration\"
                                               value=\"{{ course.duration }}\"
                                               placeholder=\"e.g., 120\"
                                               required
                                               min=\"1\"
                                               max=\"10080\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid duration in minutes.
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Price -->
                                <div class=\"col-md-3\">
                                    <div class=\"form-group\">
                                        <label for=\"price\" class=\"form-label\">
                                            <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Price (USD) <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"price\"
                                               name=\"price\"
                                               value=\"{{ course.price }}\"
                                               placeholder=\"e.g., 99.99\"
                                               required
                                               min=\"0.01\"
                                               step=\"0.01\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid price.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Course Description -->
                            <div class=\"row\">
                                <div class=\"col-12\">
                                    <div class=\"form-group\">
                                        <label for=\"description\" class=\"form-label\">
                                            <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Course Description <span class=\"text-danger\">*</span>
                                        </label>
                                        <textarea class=\"form-control enhanced-field\"
                                                  id=\"description\"
                                                  name=\"description\"
                                                  rows=\"4\"
                                                  placeholder=\"Provide a comprehensive description of the course content, objectives, and what students will learn...\"
                                                  required
                                                  maxlength=\"2000\"
                                                  style=\"border: 2px solid #ced4da; border-radius: 0.375rem; resize: vertical;\">{{ course.description }}</textarea>
                                        <div class=\"invalid-feedback\">
                                            Please provide a course description.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Learning Outcomes Section -->
                            <div class=\"row\">
                                <div class=\"col-12\">
                                    <div class=\"form-group\">
                                        <label class=\"form-label\">
                                            <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Learning Outcomes <span class=\"text-danger\">*</span>
                                        </label>
                                        <div id=\"learning-outcomes-container\">
                                            {% if course.learningOutcomes %}
                                                {% for outcome in course.learningOutcomes %}
                                                    <div class=\"input-group mb-2\">
                                                        <input type=\"text\" class=\"form-control enhanced-field\" name=\"learning_outcomes[]\" value=\"{{ outcome }}\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                        <div class=\"input-group-append\">
                                                            <button type=\"button\" class=\"btn btn-danger remove-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                <i class=\"fas fa-minus\"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            {% else %}
                                                <div class=\"input-group mb-2\">
                                                    <input type=\"text\" class=\"form-control enhanced-field\" name=\"learning_outcomes[]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                    <div class=\"input-group-append\">
                                                        <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                            <i class=\"fas fa-plus\"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <button type=\"button\" id=\"add-outcome-btn\" class=\"btn btn-outline-primary btn-sm mt-2\">
                                            <i class=\"fas fa-plus mr-1\"></i>Add Learning Outcome
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Features Section -->
                            <div class=\"row\">
                                <div class=\"col-12\">
                                    <div class=\"form-group\">
                                        <label class=\"form-label\">
                                            <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Course Features <span class=\"text-danger\">*</span>
                                        </label>
                                        <div id=\"features-container\">
                                            {% if course.features %}
                                                {% for feature in course.features %}
                                                    <div class=\"input-group mb-2\">
                                                        <input type=\"text\" class=\"form-control enhanced-field\" name=\"features[]\" value=\"{{ feature }}\" placeholder=\"e.g., Interactive exercises and quizzes\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                        <div class=\"input-group-append\">
                                                            <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                <i class=\"fas fa-minus\"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            {% else %}
                                                <div class=\"input-group mb-2\">
                                                    <input type=\"text\" class=\"form-control enhanced-field\" name=\"features[]\" placeholder=\"e.g., Interactive exercises and quizzes\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                    <div class=\"input-group-append\">
                                                        <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                            <i class=\"fas fa-plus\"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <button type=\"button\" id=\"add-feature-btn\" class=\"btn btn-outline-primary btn-sm mt-2\">
                                            <i class=\"fas fa-plus mr-1\"></i>Add Feature
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Image Upload Section -->
                            <div class=\"row\">
                                <!-- Thumbnail Image -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"thumbnail_image\" class=\"form-label\">
                                            <i class=\"fas fa-image\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Thumbnail Image (300x200px)
                                        </label>
                                        <input type=\"file\"
                                               class=\"form-control enhanced-field\"
                                               id=\"thumbnail_image\"
                                               name=\"thumbnail_image\"
                                               accept=\"image/*\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        {% if course.thumbnailImage %}
                                            <div class=\"mt-2\">
                                                <img src=\"{{ course.thumbnailUrl }}\" alt=\"Current thumbnail\" class=\"img-thumbnail\" style=\"max-width: 150px;\">
                                                <small class=\"text-muted d-block\">Current thumbnail</small>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Banner Image -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"banner_image\" class=\"form-label\">
                                            <i class=\"fas fa-panorama\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Banner Image (1200x400px)
                                        </label>
                                        <input type=\"file\"
                                               class=\"form-control enhanced-field\"
                                               id=\"banner_image\"
                                               name=\"banner_image\"
                                               accept=\"image/*\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        {% if course.bannerImage %}
                                            <div class=\"mt-2\">
                                                <img src=\"{{ course.bannerUrl }}\" alt=\"Current banner\" class=\"img-thumbnail\" style=\"max-width: 200px;\">
                                                <small class=\"text-muted d-block\">Current banner</small>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Modules Section -->
                            <div class=\"row\">
                                <div class=\"col-12\">
                                    <div class=\"form-group\">
                                        <div class=\"form-check\">
                                            <input class=\"form-check-input\" type=\"checkbox\" id=\"has_modules\" name=\"has_modules\" value=\"1\" {% if course.hasModules %}checked{% endif %}>
                                            <label class=\"form-check-label\" for=\"has_modules\">
                                                <i class=\"fas fa-puzzle-piece\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                This course has modules
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Modules Container -->
                            <div id=\"modules-container\" style=\"display: {% if course.hasModules %}block{% else %}none{% endif %};\">
                                <div class=\"row\">
                                    <div class=\"col-12\">
                                        <h5 class=\"mb-3\">
                                            <i class=\"fas fa-puzzle-piece text-primary mr-2\"></i>
                                            Course Modules
                                        </h5>
                                        <div id=\"modules-list\">
                                            {% if course.modules %}
                                                {% for module in course.modules %}
                                                    <div class=\"module-item card mb-3\" data-module-index=\"{{ loop.index0 }}\">
                                                        <div class=\"card-header bg-light\">
                                                            <div class=\"d-flex justify-content-between align-items-center\">
                                                                <h6 class=\"mb-0\">Module {{ loop.index }}</h6>
                                                                <button type=\"button\" class=\"btn btn-sm btn-danger remove-module\">
                                                                    <i class=\"fas fa-trash\"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        <div class=\"card-body\">
                                                            <input type=\"hidden\" name=\"modules[{{ loop.index0 }}][id]\" value=\"{{ module.id }}\">
                                                            <div class=\"row\">
                                                                <div class=\"col-md-6\">
                                                                    <div class=\"form-group\">
                                                                        <label class=\"form-label\">Module Code <span class=\"text-danger\">*</span></label>
                                                                        <input type=\"text\" class=\"form-control\" name=\"modules[{{ loop.index0 }}][code]\" value=\"{{ module.code }}\" required>
                                                                    </div>
                                                                </div>
                                                                <div class=\"col-md-6\">
                                                                    <div class=\"form-group\">
                                                                        <label class=\"form-label\">Module Title <span class=\"text-danger\">*</span></label>
                                                                        <input type=\"text\" class=\"form-control\" name=\"modules[{{ loop.index0 }}][title]\" value=\"{{ module.title }}\" required>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                        <button type=\"button\" id=\"add-module-btn\" class=\"btn btn-outline-primary\">
                                            <i class=\"fas fa-plus mr-1\"></i>Add Module
                                        </button>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <!-- Action Buttons Section -->
                        <div class=\"d-flex justify-content-between align-items-center flex-wrap gap-3\">
                            <button type=\"submit\" class=\"btn btn-lg btn-success\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; transition: all 0.3s ease; min-width: 200px;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                Update Course
                            </button>
                            <a href=\"{{ path('admin_courses') }}\" class=\"btn btn-lg btn-secondary\" style=\"font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; min-width: 200px; background: #6c757d; border-color: #6c757d;\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>

                    </div>
                </div>

            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Enhanced form validation
    const form = document.getElementById('course-form');

    // Bootstrap validation
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Enhanced dropdowns with Select2
    \$('.enhanced-dropdown').select2({
        placeholder: function() {
            return \$(this).data('placeholder') || 'Choose an option...';
        },
        allowClear: true,
        width: '100%',
        theme: 'bootstrap4'
    });

    // Learning Outcomes Management
    let outcomeIndex = {{ course.learningOutcomes ? course.learningOutcomes|length : 1 }};

    \$('#add-outcome-btn').click(function() {
        const container = \$('#learning-outcomes-container');
        const newOutcome = `
            <div class=\"input-group mb-2\">
                <input type=\"text\" class=\"form-control enhanced-field\" name=\"learning_outcomes[]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newOutcome);
        outcomeIndex++;
    });

    \$(document).on('click', '.remove-outcome', function() {
        if (\$('#learning-outcomes-container .input-group').length > 1) {
            \$(this).closest('.input-group').remove();
        }
    });

    // Features Management
    let featureIndex = {{ course.features ? course.features|length : 1 }};

    \$('#add-feature-btn').click(function() {
        const container = \$('#features-container');
        const newFeature = `
            <div class=\"input-group mb-2\">
                <input type=\"text\" class=\"form-control enhanced-field\" name=\"features[]\" placeholder=\"e.g., Interactive exercises and quizzes\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newFeature);
        featureIndex++;
    });

    \$(document).on('click', '.remove-feature', function() {
        if (\$('#features-container .input-group').length > 1) {
            \$(this).closest('.input-group').remove();
        }
    });

    // Modules Management
    let moduleIndex = {{ course.modules ? course.modules|length : 0 }};

    \$('#has_modules').change(function() {
        if (\$(this).is(':checked')) {
            \$('#modules-container').show();
        } else {
            \$('#modules-container').hide();
        }
    });

    \$('#add-module-btn').click(function() {
        const modulesList = \$('#modules-list');
        const newModule = `
            <div class=\"module-item card mb-3\" data-module-index=\"\${moduleIndex}\">
                <div class=\"card-header bg-light\">
                    <div class=\"d-flex justify-content-between align-items-center\">
                        <h6 class=\"mb-0\">Module \${moduleIndex + 1}</h6>
                        <button type=\"button\" class=\"btn btn-sm btn-danger remove-module\">
                            <i class=\"fas fa-trash\"></i>
                        </button>
                    </div>
                </div>
                <div class=\"card-body\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">Module Code <span class=\"text-danger\">*</span></label>
                                <input type=\"text\" class=\"form-control\" name=\"modules[\${moduleIndex}][code]\" required>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">Module Title <span class=\"text-danger\">*</span></label>
                                <input type=\"text\" class=\"form-control\" name=\"modules[\${moduleIndex}][title]\" required>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        modulesList.append(newModule);
        moduleIndex++;
    });

    \$(document).on('click', '.remove-module', function() {
        \$(this).closest('.module-item').remove();
        // Renumber modules
        \$('#modules-list .module-item').each(function(index) {
            \$(this).find('h6').text('Module ' + (index + 1));
        });
    });

    // Enhanced field styling
    \$('.enhanced-field, .enhanced-dropdown').on('focus', function() {
        \$(this).css('border-color', '#007bff');
        \$(this).css('box-shadow', '0 0 0 0.2rem rgba(0, 123, 255, 0.25)');
    }).on('blur', function() {
        \$(this).css('border-color', '#ced4da');
        \$(this).css('box-shadow', 'none');
    });

    // Initialize tooltips
    \$('[data-bs-toggle=\"tooltip\"]').tooltip();
});
</script>
{% endblock %}
", "admin/courses/edit.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\courses\\edit.html.twig");
    }
}
